<script lang="ts">
import { computed, defineComponent, toRefs, ref } from 'vue';
import MetricCard from './MetricCard.vue';
import dayjs from 'dayjs';
import { VObject } from '@/lib/vails';
import ComTravelOrderIndex from './ComTravelOrderIndex.vue';
import ComTravelActivityIndex from './ComTravelActivityIndex.vue';

const ComTravelDashboardIndex = defineComponent({
  name: 'ComTravelDashboardIndex',
  components: {
    MetricCard,
    ComTravelOrderIndex,
    ComTravelActivityIndex,
  },
  props: {
    record: { type: Object, required: true },
    orderStore: { type: Object, required: true },
    activityStore: { type: Object, required: true },
  },
  setup(props) {
    const today = dayjs().format('YYYY-mm-dd');
    const tommorrow = dayjs().add(1, 'day').format('YYYY-mm-dd');

    const metricCards = computed(() => [
      {
        title: '24小时急单跟进',
        value: props.record.activity?.spec_todo || 0,
        iconType: 'outline/clock',
        cardType: 'urgent',
        tooltipText: '出行日期为24小时以内',
        key: 'activity',
        query: {
          state_not_to_eq: 'terminated',
          start_at_in: [today, tommorrow],
        },
      },
      {
        title: '未收款订单',
        value: props.record.order?.unpaid || 0,
        iconType: 'outline/exclamation',
        cardType: 'warning',
        tooltipText: '',
        key: 'order',
        query: {
          state_not_to_eq: 'terminated',
          amount_state_eq: '未收款',
        },
      },
      {
        title: '待跟进行程',
        value: props.record.activity?.todo || 0,
        iconType: 'outline/share',
        cardType: 'info',
        tooltipText: '',
        key: 'activity',
        query: {
          state_not_to_eq: 'terminated',
          follow_state_eq: 'todo',
        },
      },
      {
        title: '未分配销售订单',
        value: props.record.order?.unlink_divide || 0,
        iconType: 'outline/user',
        cardType: 'warning',
        tooltipText: '',
        key: 'order',
        query: {
          state_not_to_eq: 'terminated',
          order_divides_id_gt: 0,
        },
      },
      {
        title: '未关联行程订单',
        value: props.record.order?.unlink_activity || 0,
        iconType: 'outline/paper-clip',
        cardType: 'info',
        tooltipText: '',
        clickType: 'unlinked-orders',
        query: {
          state_not_to_eq: 'terminated',
          activities_id_gt: 0,
        },
      },
      {
        title: '本日出行行程',
        value: props.record.activity?.today || 0,
        iconType: 'outline/calendar',
        cardType: 'success',
        tooltipText: '',
        key: 'activity',
        query: {
          state_not_to_eq: 'terminated',
          start_at_eq: today,
        },
      },
      {
        title: '次日出行行程',
        value: props.record.activity?.tomorrow || 0,
        iconType: 'outline/calendar',
        cardType: 'info',
        tooltipText: '',
        key: 'activity',
        query: {
          state_not_to_eq: 'terminated',
          start_at_eq: tommorrow,
        },
      },
    ]);

    const visible = ref(false);
    const activeCard = ref<any>({});
    const onClick = (card: VObject) => {
      visible.value = true;
      activeCard.value = card;
    };

    return {
      ...toRefs(props),
      metricCards,
      visible,
      activeCard,
      onClick,
    };
  },
});

export default ComTravelDashboardIndex;
</script>

<template lang="pug">
.com-travel-dashboard-index.main.flex-1.overflow-y-auto.p-6
  .mb-6
    h1.text-2xl.font-bold.text-gray-800 统计面板

  // Statistics Cards Section
  .bg-white.rounded-lg.shadow.p-6.mb-6(style="overflow: visible")
    .flex.items-center.justify-between.mb-4
      h2.text-lg.font-medium.text-gray-800.flex.items-center
        TaIcon.w-6.h-6.mr-2.text-blue-500(type='outline/chart-bar')
        | 待办统计面板
    //- 待办统计面板 - 7个统计数字
    .grid.grid-cols-1.gap-4(class="sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7")
      MetricCard(
        v-for="(record, index) in metricCards",
        :record='record',
        @click="onClick"
      )

  // Data Overview Section
  .bg-white.rounded-lg.shadow.p-6.mb-6
    .flex.items-center.justify-between.mb-6
      h2.text-lg.font-medium.text-gray-800.flex.items-center
        TaIcon.w-6.h-6.text-red-500.mr-2(type="outline/trending-up")
        | 数据概览面板
    // Filter Controls Section
    .bg-gray-50.rounded-lg.p-4.mb-6.border.border-gray-200
      .grid.grid-cols-1.gap-4.items-center(class="md:grid-cols-4")
        // 日期类型
        .info
          label.block.text-xs.text-gray-600.mb-1 日期类型
          .relative
            select.w-full.text-sm.border.border-gray-300.rounded.px-3.py-2.appearance-none.bg-white.pr-8(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
              option(value="booking_date") 预订日期
              option(value="travel_date") 出发日期
            .absolute.inset-y-0.right-0.flex.items-center.px-2.pointer-events-none
              i.fas.fa-chevron-down.text-gray-400.text-xs
        // 时间范围
        .info
          label.block.text-xs.text-gray-600.mb-1 时间范围
          .relative
            select.w-full.text-sm.border.border-gray-300.rounded.px-3.py-2.appearance-none.bg-white.pr-8(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
              option(value="yesterday") 昨天
              option(value="7days" selected) 近7天
              option(value="30days") 近30天
              option(value="custom") 自定义
            .absolute.inset-y-0.right-0.flex.items-center.px-2.pointer-events-none
              i.fas.fa-chevron-down.text-gray-400.text-xs
        // 自定义日期范围
        .info(class="md:col-span-2")
          label.block.text-xs.text-gray-600.mb-1 日期范围
          .flex.items-center.space-x-2
            input(type="date").flex-1.text-sm.border.border-gray-300.rounded.px-3.py-2(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
            span.text-gray-500.text-sm -
            input(type="date").flex-1.text-sm.border.border-gray-300.rounded.px-3.py-2(class="focus:border-blue-500 focus:ring-1 focus:ring-blue-500")
      // 分类项筛选
      .mt-6.pt-4.border-t.border-gray-200
        .flex.items-center.justify-between.mb-4
          h3.text-sm.font-semibold.text-gray-700.flex.items-center
            TaIcon.text-blue-500.mr-2(type="outline/filter")
            | 分类筛选
          button.text-xs.text-gray-500.transition-colors(@click="clearAllFilters" class="hover:text-gray-700") 清除所有
        .grid.grid-cols-1.gap-4(class="sm:grid-cols-2 lg:grid-cols-4")
          //- 销售筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input.sr-only(id="sales-filter" type="checkbox")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="sales-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 销售
              TaIcon.text-blue-400.text-sm(type="outline/user")
            .sales-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400.focus:ring-2.focus:ring-blue-100" type="text" placeholder="搜索销售...")
          //- 渠道筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="group-hover:border-blue-400")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="channel-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="channel-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 渠道
              TaIcon.text-green-400.text-sm(type="outline/share")
            .channel-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索订单渠道...")
          //- 收款方式筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="payment-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="payment-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 收款方式
              TaIcon.text-purple-400.text-sm(type="outline/credit-card")
            .payment-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索收款方式...")
          //- 国家筛选
          .bg-white.border.border-gray-200.rounded-lg.p-4.transition-all.duration-200.flex.flex-col.items-start(class="hover:border-blue-300")
            .flex.items-center.justify-between.w-full
              label.flex.items-center.cursor-pointer.group
                .relative.flex.items-center.justify-center
                  input(type="checkbox").sr-only(id="country-filter")
                  .w-4.h-4.border-2.border-gray-300.rounded.bg-white.transition-colors.filter-checkbox.flex.items-center.justify-center(data-target="country-filter" class="group-hover:border-blue-400")
                    i.fas.fa-check.text-white.text-xs.opacity-0.transition-opacity
                span.ml-3.text-sm.font-medium.text-gray-700(class="group-hover:text-gray-900") 国家
              TaIcon.text-orange-400.text-sm(type="outline/globe")
            .country-search.hidden.transition-all.duration-300.mt-3.w-full
              input.w-full.text-sm.border.border-gray-200.rounded-md.px-3.py-2.transition-all(class="focus:border-blue-400 focus:ring-2 focus:ring-blue-100" type="text" placeholder="搜索国家...")
    // Top Metrics Row
    .grid.grid-cols-1.gap-5.mb-6(class="md:grid-cols-2 xl:grid-cols-3")
      // 应收总额
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 应收总额
            .text-xs.text-gray-400 Receivable Amount
          .w-8.h-8.bg-red-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-red-100")
            TaIcon.text-red-500.text-sm(type="outline/arrow-up")
        .space-y-4
          .text-3xl.font-bold.text-gray-900 ¥15,000
          .flex.items-center.space-x-2
            .flex.items-center.space-x-1
              .w-2.h-2.bg-red-500.rounded-full
              span.text-xs.font-medium.text-red-600 +15%
            span.text-xs.text-gray-500 较前一周期
      // 实收总额
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 实收总额
            .text-xs.text-gray-400 Received Amount
          .w-8.h-8.bg-green-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-green-100")
            TaIcon.text-green-500.text-sm(type="outline/arrow-down")
        .space-y-4
          .text-3xl.font-bold.text-gray-900 ¥11,250
          .flex.items-center.space-x-2
            .flex.items-center.space-x-1
              .w-2.h-2.bg-green-500.rounded-full
              span.text-xs.font-medium.text-green-600 -12%
            span.text-xs.text-gray-500 较前一周期
      // 收支分布
      .bg-white.rounded-2xl.border.border-gray-100.p-6.transition-all.duration-300.group(class="hover:shadow-lg")
        .flex.justify-between.items-start.mb-6
          div
            h3.text-gray-600.text-sm.font-medium.mb-1 收支分布
            .text-xs.text-gray-400 Revenue Distribution
          .w-8.h-8.bg-purple-50.rounded-lg.flex.items-center.justify-center.transition-colors(class="group-hover:bg-purple-100")
            TaIcon.text-purple-500.text-sm(type="outline/chart-pie")
        .flex.items-center.space-x-6
          // 圆环图
          .relative
            .w-20.h-20.relative
              svg.w-20.h-20.transform.-rotate-90(viewBox="0 0 32 32")
                circle(cx="16" cy="16" r="12" fill="none" stroke="#f3f4f6" stroke-width="4")
                circle(cx="16" cy="16" r="12" fill="none" stroke="#10b981" stroke-width="4" stroke-dasharray="56.5 75.4" stroke-linecap="round")
              .absolute.inset-0.flex.items-center.justify-center
                span.text-sm.font-bold.text-gray-700 75%
          // 数据列表
          .flex-1.space-y-3
            .flex.items-center.justify-between
              .flex.items-center.space-x-3
                .w-3.h-3.bg-green-500.rounded-full
                span.text-sm.font-medium.text-gray-700 已收
              span.text-lg.font-bold.text-gray-900 ¥11,250
            .flex.items-center.justify-between
              .flex.items-center.space-x-3
                .w-3.h-3.bg-gray-300.rounded-full
                span.text-sm.font-medium.text-gray-700 未收
              span.text-lg.font-bold.text-gray-900 ¥3,750
  TaNoPaddingDrawer(
    v-model:visible='visible',
    v-if='visible',
    :title='activeCard.title',
    width='800px',
  )
    ComTravelOrderIndex(
      :store='orderStore',
      :query='activeCard.query',
      v-if='activeCard.key === "order"'
    )
    ComTravelActivityIndex(
      :store='activityStore',
      :query='activeCard.query',
      v-else
    )
</template>

<style lang="stylus" scoped>
.com-travel-manage-notices-index {
  height: 100%;
  width: 100%;
}

.percentage-positive {
  color: #10b981;
}

.percentage-negative {
  color: #ef4444;
}

.percentage-neutral {
  color: #6b7280;
}
</style>
