<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import { VObject } from '@/lib/vails/model';

const ComTravelActivityIndex = defineComponent({
  name: 'ComTravelActivityIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
    query: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '行程',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'travel_activity',
      detail: {
        mode: 'route',
        // mode: 'drawer',
        // width: '1100px',
      },
      query: {
        ...props.query,
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: false },
        { key: 'export', enabled: false },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      key1: 0,
      key2: 0,
    });

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    return {
      ...toRefs(props),
      config,
      onIndex,
    };
  },
});

export default ComTravelActivityIndex;
</script>

<template lang="pug">
.com-travel-activity-index
  TaIndexView(:config='config', @onIndex='onIndex')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComTravelNoticesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/travel/manage/notices/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-travel-activity-index {
  height: 100%;
  width: 100%;
}
</style>
