<script lang="ts">
import { defineComponent, onMounted, ref } from '@vue/runtime-core';
import ComTravelDashboardIndex from '@/engines/travel/components/travel/dashboard/ComTravelDashboardIndex.vue';
import { VStore } from '@/lib/vails';
import { TravelManageDashboardApi } from '@/engines/travel/travel-core/apis/travel/manage/dashboard.api';
import { TravelManageActivitiesApi } from '@/engines/travel/travel-core/apis/travel/manage/activities.api';
import { TravelActivityModel } from '@/engines/travel/travel-core/models/travel/manage/activities';
import { TravelManageOrderApi } from '@/engines/travel/travel-core/apis/travel/manage/order.api';
import { TravelOrderModel } from '@/engines/travel/travel-core/models/travel/manage/order';

const TravelManageDashboardIndex = defineComponent({
  name: 'TravelManageDashboardIndex',
  components: {
    ComTravelDashboardIndex,
  },
  setup() {
    const store = new VStore(new TravelManageDashboardApi());
    const activityStore = new VStore(new TravelManageActivitiesApi(), TravelActivityModel);
    const orderStore = new VStore(new TravelManageOrderApi(), TravelOrderModel);

    const record = ref({});
    onMounted(async () => {
      record.value = await store.create({});
    });

    return {
      record,
      activityStore,
      orderStore,
    };
  },
});

export default TravelManageDashboardIndex;
</script>

<template lang="pug">
.travel-manage-dashboard-index
  ComTravelDashboardIndex(
    :record='record'
    :activityStore='activityStore',
    :orderStore='orderStore'
    v-if='record.order'
  )
</template>

<style lang="stylus" scoped>
.travel-manage-dashboard-index {
  height: 100%;
  width: 100%;
}
</style>
